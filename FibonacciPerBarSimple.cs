//
// Simplified Fibonacci Per Bar Indicator - NinjaScript Version
// Basic version to test compilation
//
#region Using declarations
using System;
using System.ComponentModel.DataAnnotations;
using System.Windows.Media;
using NinjaTrader.Cbi;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

namespace NinjaTrader.NinjaScript.Indicators
{
    public class FibonacciPerBarSimple : Indicator
    {
        #region Variables
        // Display Settings
        private bool showLevels = true;
        private bool showLabels = true;
        private bool showTrendlines = true;
        private int maxBarsBack = 50;
        private int lineWidth = 1;
        private int trendlineWidth = 2;

        // Signal Settings
        private bool showSignals = true;
        private bool showImmediateSignals = true;

        // Colors
        private Brush fibColor10 = Brushes.Green;
        private Brush fibColor00 = Brushes.Red;
        private Brush textColor = Brushes.White;
        private Brush trendlineColor = Brushes.White;

        // Fibonacci levels and colors
        private readonly double[] fibLevels = { 1.1, 1.08, 1.0, 0.9, 0.1, 0.0, -0.08, -0.1 };
        private Brush[] fibColors;

        // Signal tracking variables
        private double currentGreenLevel = double.NaN;
        private double currentRedLevel = double.NaN;
        private bool greenSignalTriggered = false;
        private bool redSignalTriggered = false;

        // Position tracking
        private bool activeBuyPosition = false;
        private bool activeSellPosition = false;
        private double buyTpLevel = double.NaN;
        private double sellTpLevel = double.NaN;
        private double buySlLevel = double.NaN;
        private double sellSlLevel = double.NaN;
        #endregion

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = "Simplified Fibonacci Per Bar Indicator";
                Name = "FibonacciPerBarSimple";
                Calculate = Calculate.OnBarClose;
                IsOverlay = true;
                DisplayInDataBox = true;
                DrawOnPricePanel = true;
                PaintPriceMarkers = true;
                ScaleJustification = NinjaTrader.Gui.Chart.ScaleJustification.Right;
                IsSuspendedWhileInactive = true;

                // Initialize fib colors array
                fibColors = new Brush[] { 
                    Brushes.White, Brushes.White, fibColor10, Brushes.Orange, 
                    Brushes.Purple, fibColor00, Brushes.White, Brushes.White 
                };
            }
        }

        protected override void OnBarUpdate()
        {
            if (CurrentBar < 1)
                return;

            if (!showLevels)
                return;

            // Calculate Fibonacci levels based on current bar high/low
            double currentHigh = High[0];
            double currentLow = Low[0];
            double priceRange = currentHigh - currentLow;

            if (priceRange > 0)
            {
                // Calculate current levels
                currentGreenLevel = currentLow + (priceRange * 1.0);  // 100% level
                currentRedLevel = currentLow + (priceRange * 0.0);    // 0% level

                // Reset signal flags each bar
                greenSignalTriggered = false;
                redSignalTriggered = false;

                // Draw Fibonacci levels
                DrawFibonacciLevels(currentLow, priceRange);

                // Check for signals
                CheckImmediateSignals();

                // Check for take profit and stop loss
                CheckTakeProfitAndStopLoss();
            }
        }

        private void DrawFibonacciLevels(double rangeLow, double priceRange)
        {
            if (!showLevels)
                return;

            for (int i = 0; i < fibLevels.Length; i++)
            {
                double fibLevel = fibLevels[i];
                double fibPrice = rangeLow + (priceRange * fibLevel);
                Brush fibColor = fibColors[i];

                // Create horizontal line
                string lineTag = $"FibLine_{CurrentBar}_{i}";
                Draw.Line(this, lineTag, false, 0, fibPrice, 1, fibPrice, fibColor, DashStyleHelper.Solid, lineWidth);

                // Add label if enabled
                if (showLabels)
                {
                    string labelText = $"{fibLevel:F2} ({fibPrice:F3})";
                    string labelTag = $"FibLabel_{CurrentBar}_{i}";
                    Draw.Text(this, labelTag, false, labelText, 0, fibPrice, 0, textColor, new SimpleFont("Arial", 8), TextAlignment.Left, Brushes.Transparent, Brushes.Transparent, 0);
                }
            }

            // Draw trend line if enabled
            if (showTrendlines)
            {
                string trendTag = $"TrendLine_{CurrentBar}";
                Draw.Line(this, trendTag, false, 0, rangeLow, 0, rangeLow + priceRange, trendlineColor, DashStyleHelper.Dash, trendlineWidth);
            }
        }

        private void CheckImmediateSignals()
        {
            if (!showSignals || !showImmediateSignals)
                return;

            if (double.IsNaN(currentGreenLevel) || double.IsNaN(currentRedLevel))
                return;

            // Check for buy signal (price hits green level)
            if (!greenSignalTriggered && High[0] >= currentGreenLevel && Low[0] <= currentGreenLevel)
            {
                CreateSignalDot("BUY", currentGreenLevel);
                greenSignalTriggered = true;
                
                // Update position tracking
                activeBuyPosition = true;
                activeSellPosition = false;
                
                // Calculate TP and SL levels
                double priceRange = currentGreenLevel - currentRedLevel;
                buyTpLevel = currentRedLevel + (priceRange * 1.1);  // 1.1 level
                buySlLevel = currentRedLevel;  // Stop loss at red line
            }

            // Check for sell signal (price hits red level)
            if (!redSignalTriggered && Low[0] <= currentRedLevel && High[0] >= currentRedLevel)
            {
                CreateSignalDot("SELL", currentRedLevel);
                redSignalTriggered = true;
                
                // Update position tracking
                activeSellPosition = true;
                activeBuyPosition = false;
                
                // Calculate TP and SL levels
                double priceRange = currentGreenLevel - currentRedLevel;
                sellTpLevel = currentRedLevel + (priceRange * -0.1);  // -0.1 level
                sellSlLevel = currentGreenLevel;  // Stop loss at green line
            }
        }

        private void CheckTakeProfitAndStopLoss()
        {
            // Check buy position TP/SL
            if (activeBuyPosition)
            {
                if (!double.IsNaN(buyTpLevel) && High[0] >= buyTpLevel && Low[0] <= buyTpLevel)
                {
                    CreateSignalDot("TP", buyTpLevel);
                    activeBuyPosition = false;
                    buyTpLevel = double.NaN;
                    buySlLevel = double.NaN;
                }
                else if (!double.IsNaN(buySlLevel) && Low[0] <= buySlLevel && High[0] >= buySlLevel)
                {
                    CreateSignalDot("SL", buySlLevel);
                    activeBuyPosition = false;
                    buyTpLevel = double.NaN;
                    buySlLevel = double.NaN;
                }
            }

            // Check sell position TP/SL
            if (activeSellPosition)
            {
                if (!double.IsNaN(sellTpLevel) && Low[0] <= sellTpLevel && High[0] >= sellTpLevel)
                {
                    CreateSignalDot("TP", sellTpLevel);
                    activeSellPosition = false;
                    sellTpLevel = double.NaN;
                    sellSlLevel = double.NaN;
                }
                else if (!double.IsNaN(sellSlLevel) && High[0] >= sellSlLevel && Low[0] <= sellSlLevel)
                {
                    CreateSignalDot("SL", sellSlLevel);
                    activeSellPosition = false;
                    sellTpLevel = double.NaN;
                    sellSlLevel = double.NaN;
                }
            }
        }

        private void CreateSignalDot(string signalType, double priceLevel)
        {
            Brush signalColor = Brushes.White;
            string arrowText = "▶";

            switch (signalType)
            {
                case "BUY":
                    signalColor = Brushes.Green;
                    break;
                case "SELL":
                    signalColor = Brushes.Red;
                    break;
                case "TP":
                    signalColor = Brushes.Blue;
                    arrowText = "TP";
                    break;
                case "SL":
                    signalColor = Brushes.Orange;
                    arrowText = "SL";
                    break;
            }

            string signalTag = $"Signal_{signalType}_{CurrentBar}";
            Draw.Text(this, signalTag, false, arrowText, 0, priceLevel, 0, signalColor, new SimpleFont("Arial", 12), TextAlignment.Center, Brushes.Transparent, Brushes.Transparent, 0);

            // Log signal
            Print($"{signalType} signal at {priceLevel:F3} on bar {CurrentBar}");
        }

        #region Properties
        [NinjaScriptProperty]
        [Display(Name = "Show Fibonacci Levels", Order = 1, GroupName = "Display")]
        public bool ShowLevels
        {
            get { return showLevels; }
            set { showLevels = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Show Level Labels", Order = 2, GroupName = "Display")]
        public bool ShowLabels
        {
            get { return showLabels; }
            set { showLabels = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Show Buy/Sell Signals", Order = 1, GroupName = "Signals")]
        public bool ShowSignals
        {
            get { return showSignals; }
            set { showSignals = value; }
        }
        #endregion
    }
}
