//
// Combined Fibonacci & MTF Candle Indicator - NinjaScript Version
// Converted from Pine Script with exact same logic and settings
//
#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Windows.Media;
using NinjaTrader.Data;
using NinjaTrader.Gui.Chart;
using NinjaTrader.NinjaScript;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

namespace NinjaTrader.NinjaScript.Indicators
{
    public class FibonacciPerBarIndicator : Indicator
    {
        #region Variables
        // Display Settings
        private bool showLevels = true;
        private bool showLabels = true;
        private bool showTrendlines = true;
        private int maxBarsBack = 50;
        private int lineWidth = 1;
        private int trendlineWidth = 2;
        private int transparency = 20;

        // Mode Settings
        private bool useMtfMode = false;
        private BarsPeriodType mtfPeriodType = BarsPeriodType.Minute;
        private int mtfPeriodValue = 5;
        private bool useConfirmedData = true;

        // Legacy Settings
        private bool useHigherTimeframe = false;
        private BarsPeriodType htfPeriodType = BarsPeriodType.Minute;
        private int htfPeriodValue = 5;

        // Trading Hours Settings
        private readonly bool showTradingHoursBg = true;
        private readonly TimeSpan tradingStart = new TimeSpan(9, 30, 0);
        private readonly TimeSpan tradingEnd = new TimeSpan(16, 0, 0);

        // Colors
        private Brush fibColor11 = Brushes.White;
        private Brush fibColor108 = Brushes.White;
        private Brush fibColor10 = Brushes.Green;
        private Brush fibColor00 = Brushes.Red;
        private Brush fibColorNeg08 = Brushes.White;
        private Brush fibColorNeg1 = Brushes.White;
        private Brush textColor = Brushes.White;
        private Brush trendlineColor = Brushes.White;
        private Brush outsideHoursColor = Brushes.Red;

        // Signal Settings
        private bool showSignals = true;
        private bool showImmediateSignals = true;
        private bool showDelayedSignals = true;

        // Fibonacci levels and colors
        private readonly double[] fibLevels = { 1.1, 1.08, 1.0, 0.9, 0.1, 0.0, -0.08, -0.1 };
        private Brush[] fibColors;

        // Signal tracking variables
        private double currentGreenLevel = double.NaN;
        private double currentRedLevel = double.NaN;
        private double prevGreenLevel = double.NaN;
        private double prevRedLevel = double.NaN;
        private bool htfJustClosed = false;
        private int htfCloseBar = -1;

        private readonly double newestGreenLevel = double.NaN;
        private readonly double newestRedLevel = double.NaN;

        private bool greenSignalTriggered = false;
        private bool redSignalTriggered = false;

        // Position tracking
        private bool activeBuyPosition = false;
        private bool activeSellPosition = false;
        private double buyTpLevel = double.NaN;
        private double sellTpLevel = double.NaN;
        private double buySlLevel = double.NaN;
        private double sellSlLevel = double.NaN;

        // MTF data variables
        private double mtfOpenValue;
        private double mtfHighValue;
        private double mtfLowValue;
        private double mtfCloseValue;

        // HTF data variables
        private double htfHighValue;
        private double htfLowValue;

        // Object tracking for cleanup
        private Queue<string> objectTagQueue = new Queue<string>();
        #endregion

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = "Combined Fibonacci & MTF Candle Indicator - Exact Pine Script conversion";
                Name = "FibonacciPerBarIndicator";
                Calculate = Calculate.OnBarClose;
                IsOverlay = true;
                DisplayInDataBox = true;
                DrawOnPricePanel = true;
                DrawHorizontalGridLines = true;
                DrawVerticalGridLines = true;
                PaintPriceMarkers = true;
                ScaleJustification = ScaleJustification.Right;
                IsSuspendedWhileInactive = true;

                // Initialize fib colors array
                fibColors = new Brush[] { fibColor11, fibColor108, fibColor10, Brushes.Orange, Brushes.Purple, fibColor00, fibColorNeg08, fibColorNeg1 };
            }
            else if (State == State.Configure)
            {
                // Add MTF data series if enabled
                if (useMtfMode)
                {
                    AddDataSeries(Instrument.MasterInstrument.Name, new BarsPeriod { BarsPeriodType = mtfPeriodType, Value = mtfPeriodValue }, Instrument.TradingHours.Name);
                }

                // Add HTF data series if enabled
                if (useHigherTimeframe)
                {
                    AddDataSeries(Instrument.MasterInstrument.Name, new BarsPeriod { BarsPeriodType = htfPeriodType, Value = htfPeriodValue }, Instrument.TradingHours.Name);
                }
            }
            else if (State == State.DataLoaded)
            {
                // Initialize variables
                mtfOpenValue = double.NaN;
                mtfHighValue = double.NaN;
                mtfLowValue = double.NaN;
                mtfCloseValue = double.NaN;
                htfHighValue = double.NaN;
                htfLowValue = double.NaN;
            }
        }

        protected override void OnBarUpdate()
        {
            if (CurrentBar < 1)
                return;

            // Update MTF data if enabled
            if (useMtfMode && BarsArray.Length > 1 && CurrentBars[1] >= 0)
            {
                UpdateMtfData();
            }

            // Update HTF data if enabled
            if (useHigherTimeframe && BarsArray.Length > (useMtfMode ? 2 : 1))
            {
                int htfIndex = useMtfMode ? 2 : 1;
                if (CurrentBars[htfIndex] >= 0)
                {
                    UpdateHtfData(htfIndex);
                }
            }

            // Check if we're in trading hours
            bool isInTradingHours = IsInTradingHours();
            
            if (!showLevels || (showTradingHoursBg && !isInTradingHours))
                return;

            // Main execution logic
            if (useMtfMode)
            {
                ProcessMtfMode();
            }
            else
            {
                ProcessLegacyMode();
            }

            // Check for delayed signals
            CheckDelayedSignals();

            // Check for take profit and stop loss conditions
            CheckTakeProfitAndStopLoss();

            // Clean up old objects
            CleanupOldObjects();
        }

        private void UpdateMtfData()
        {
            if (BarsArray.Length <= 1 || CurrentBars[1] < 0)
                return;

            int mtfIndex = useConfirmedData ? 1 : 0;
            if (CurrentBars[1] >= mtfIndex)
            {
                mtfOpenValue = Opens[1][mtfIndex];
                mtfHighValue = Highs[1][mtfIndex];
                mtfLowValue = Lows[1][mtfIndex];
                mtfCloseValue = Closes[1][mtfIndex];
            }
        }

        private void UpdateHtfData(int htfIndex)
        {
            if (CurrentBars[htfIndex] >= 0)
            {
                htfHighValue = Highs[htfIndex][0];
                htfLowValue = Lows[htfIndex][0];
            }
        }

        private bool IsInTradingHours()
        {
            if (!showTradingHoursBg)
                return true;

            TimeSpan currentTime = Time[0].TimeOfDay;
            return currentTime >= tradingStart && currentTime <= tradingEnd;
        }

        private void ProcessMtfMode()
        {
            if (BarsArray.Length <= 1 || CurrentBars[1] < 1)
                return;

            // Detect new MTF period
            bool isNewMtfPeriod = IsNewMtfPeriod();
            
            if (isNewMtfPeriod && !double.IsNaN(mtfOpenValue) && !double.IsNaN(mtfHighValue) &&
                !double.IsNaN(mtfLowValue) && !double.IsNaN(mtfCloseValue))
            {
                ProcessNewMtfPeriod();
            }
            else
            {
                // Check for immediate signals on every bar
                CheckImmediateSignals();
            }
        }

        private void ProcessLegacyMode()
        {
            double currentHigh = useHigherTimeframe && !double.IsNaN(htfHighValue) ? htfHighValue : High[0];
            double currentLow = useHigherTimeframe && !double.IsNaN(htfLowValue) ? htfLowValue : Low[0];
            double priceRange = currentHigh - currentLow;

            if (priceRange > 0)
            {
                // Calculate current levels
                currentGreenLevel = currentLow + (priceRange * 1.0);  // 100% level
                currentRedLevel = currentLow + (priceRange * 0.0);    // 0% level

                // Draw Fibonacci levels
                DrawFibonacciLevels(currentLow, priceRange, CurrentBar, CurrentBar + 1);

                // Check for signals
                CheckImmediateSignals();
            }
        }

        private bool IsNewMtfPeriod()
        {
            if (BarsArray.Length <= 1 || CurrentBars[1] < 1)
                return false;

            return Times[1][0] != Times[1][1];
        }

        private void ProcessNewMtfPeriod()
        {
            // Store previous levels
            if (!double.IsNaN(currentGreenLevel) && !double.IsNaN(currentRedLevel))
            {
                prevGreenLevel = currentGreenLevel;
                prevRedLevel = currentRedLevel;
                htfJustClosed = true;
                htfCloseBar = CurrentBar;
            }

            // Calculate new levels using MTF data
            double rangeHigh = mtfHighValue;
            double rangeLow = mtfLowValue;
            double priceRange = rangeHigh - rangeLow;

            if (priceRange > 0)
            {
                currentGreenLevel = rangeLow + (priceRange * 1.0);  // 100% level
                currentRedLevel = rangeLow + (priceRange * 0.0);    // 0% level

                // Reset signal flags
                greenSignalTriggered = false;
                redSignalTriggered = false;

                // Draw Fibonacci levels for MTF period
                DrawFibonacciLevels(rangeLow, priceRange, CurrentBar - GetMtfBarCount(), CurrentBar);

                // Check for immediate signals
                CheckImmediateSignals();
            }
        }

        private int GetMtfBarCount()
        {
            // Calculate approximate number of bars in MTF period
            if (BarsArray.Length <= 1)
                return 1;

            TimeSpan mtfDuration = new TimeSpan(0, mtfPeriodValue, 0);
            TimeSpan currentTfDuration = BarsPeriod.Value == 1 ? new TimeSpan(0, 1, 0) : new TimeSpan(0, BarsPeriod.Value, 0);

            return Math.Max(1, (int)(mtfDuration.TotalMinutes / currentTfDuration.TotalMinutes));
        }

        private void DrawFibonacciLevels(double rangeLow, double priceRange, int startBar, int endBar)
        {
            if (!showLevels)
                return;

            for (int i = 0; i < fibLevels.Length; i++)
            {
                double fibLevel = fibLevels[i];
                double fibPrice = rangeLow + (priceRange * fibLevel);
                Brush fibColor = fibColors[i];

                // Create horizontal line
                string lineTag = $"FibLine_{CurrentBar}_{i}";
                Draw.Line(this, lineTag, false, startBar, fibPrice, endBar, fibPrice, fibColor, DashStyleHelper.Solid, lineWidth);
                objectTagQueue.Enqueue(lineTag);

                // Add label if enabled
                if (showLabels)
                {
                    string labelText = $"{fibLevel:F2} ({fibPrice:F3})";
                    if (useMtfMode)
                        labelText += $" [MTF {mtfPeriodValue}m]";
                    else if (useHigherTimeframe)
                        labelText += $" [HTF {htfPeriodValue}m]";

                    string labelTag = $"FibLabel_{CurrentBar}_{i}";
                    Draw.Text(this, labelTag, false, labelText, startBar, fibPrice, 0, textColor, new SimpleFont("Arial", 8), TextAlignment.Left, Brushes.Transparent, Brushes.Transparent, 0);
                    objectTagQueue.Enqueue(labelTag);
                }
            }

            // Draw trend line if enabled
            if (showTrendlines)
            {
                double trendStartY = rangeLow;
                double trendEndY = rangeLow + priceRange;

                string trendTag = $"TrendLine_{CurrentBar}";
                Draw.Line(this, trendTag, false, startBar, trendStartY, endBar, trendEndY, trendlineColor, DashStyleHelper.Dash, trendlineWidth);
                objectTagQueue.Enqueue(trendTag);
            }
        }

        private void CheckImmediateSignals()
        {
            if (!showSignals || !showImmediateSignals)
                return;

            if (double.IsNaN(currentGreenLevel) || double.IsNaN(currentRedLevel))
                return;

            // Check for buy signal (price hits green level)
            if (!greenSignalTriggered && High[0] >= currentGreenLevel && Low[0] <= currentGreenLevel)
            {
                CreateSignalDot("BUY", currentGreenLevel, false);
                greenSignalTriggered = true;

                // Update position tracking
                activeBuyPosition = true;
                activeSellPosition = false;
                sellTpLevel = double.NaN;
                sellSlLevel = double.NaN;

                // Calculate TP and SL levels
                double priceRange = currentGreenLevel - currentRedLevel;
                buyTpLevel = currentRedLevel + (priceRange * 1.1);  // 1.1 level
                buySlLevel = currentRedLevel;  // Stop loss at red line

                // Check if TP/SL hit on same bar
                CheckSameBartpSl();
            }

            // Check for sell signal (price hits red level)
            if (!redSignalTriggered && Low[0] <= currentRedLevel && High[0] >= currentRedLevel)
            {
                CreateSignalDot("SELL", currentRedLevel, false);
                redSignalTriggered = true;

                // Update position tracking
                activeSellPosition = true;
                activeBuyPosition = false;
                buyTpLevel = double.NaN;
                buySlLevel = double.NaN;

                // Calculate TP and SL levels
                double priceRange = currentGreenLevel - currentRedLevel;
                sellTpLevel = currentRedLevel + (priceRange * -0.1);  // -0.1 level
                sellSlLevel = currentGreenLevel;  // Stop loss at green line

                // Check if TP/SL hit on same bar
                CheckSameBartpSl();
            }
        }

        private void CheckDelayedSignals()
        {
            if (!showSignals || !showDelayedSignals || !htfJustClosed)
                return;

            double signalGreen = !double.IsNaN(newestGreenLevel) ? newestGreenLevel :
                                (!double.IsNaN(currentGreenLevel) ? currentGreenLevel : prevGreenLevel);
            double signalRed = !double.IsNaN(newestRedLevel) ? newestRedLevel :
                              (!double.IsNaN(currentRedLevel) ? currentRedLevel : prevRedLevel);

            if (double.IsNaN(signalGreen) || double.IsNaN(signalRed))
                return;

            // Check for delayed buy signal
            if (High[0] >= signalGreen && Low[0] <= signalGreen && !greenSignalTriggered)
            {
                CreateSignalDot("BUY", signalGreen, true);
                greenSignalTriggered = true;
                htfJustClosed = false;

                // Update position tracking and calculate TP/SL
                activeBuyPosition = true;
                activeSellPosition = false;
                double priceRange = signalGreen - signalRed;
                buyTpLevel = signalRed + (priceRange * 1.1);
                buySlLevel = signalRed;
                sellTpLevel = double.NaN;
                sellSlLevel = double.NaN;

                CheckSameBartpSl();
            }

            // Check for delayed sell signal
            if (Low[0] <= signalRed && High[0] >= signalRed && !redSignalTriggered)
            {
                CreateSignalDot("SELL", signalRed, true);
                redSignalTriggered = true;
                htfJustClosed = false;

                // Update position tracking and calculate TP/SL
                activeSellPosition = true;
                activeBuyPosition = false;
                double priceRange = signalGreen - signalRed;
                sellTpLevel = signalRed + (priceRange * -0.1);
                sellSlLevel = signalGreen;
                buyTpLevel = double.NaN;
                buySlLevel = double.NaN;

                CheckSameBartpSl();
            }
        }

        private void CheckSameBartpSl()
        {
            // Check if TP is hit on same bar as signal
            if (activeBuyPosition && !double.IsNaN(buyTpLevel) && High[0] >= buyTpLevel && Low[0] <= buyTpLevel)
            {
                CreateSignalDot("TP", buyTpLevel, false);
                activeBuyPosition = false;
                buyTpLevel = double.NaN;
                buySlLevel = double.NaN;
            }
            else if (activeBuyPosition && !double.IsNaN(buySlLevel) && Low[0] <= buySlLevel && High[0] >= buySlLevel)
            {
                CreateSignalDot("SL", buySlLevel, false);
                activeBuyPosition = false;
                buyTpLevel = double.NaN;
                buySlLevel = double.NaN;
            }

            if (activeSellPosition && !double.IsNaN(sellTpLevel) && Low[0] <= sellTpLevel && High[0] >= sellTpLevel)
            {
                CreateSignalDot("TP", sellTpLevel, false);
                activeSellPosition = false;
                sellTpLevel = double.NaN;
                sellSlLevel = double.NaN;
            }
            else if (activeSellPosition && !double.IsNaN(sellSlLevel) && High[0] >= sellSlLevel && Low[0] <= sellSlLevel)
            {
                CreateSignalDot("SL", sellSlLevel, false);
                activeSellPosition = false;
                sellTpLevel = double.NaN;
                sellSlLevel = double.NaN;
            }
        }

        private void CheckTakeProfitAndStopLoss()
        {
            if (!activeBuyPosition && !activeSellPosition)
                return;

            // Check buy position TP/SL
            if (activeBuyPosition)
            {
                if (!double.IsNaN(buyTpLevel) && High[0] >= buyTpLevel && Low[0] <= buyTpLevel)
                {
                    CreateSignalDot("TP", buyTpLevel, false);
                    activeBuyPosition = false;
                    buyTpLevel = double.NaN;
                    buySlLevel = double.NaN;
                }
                else if (!double.IsNaN(buySlLevel) && Low[0] <= buySlLevel && High[0] >= buySlLevel)
                {
                    CreateSignalDot("SL", buySlLevel, false);
                    activeBuyPosition = false;
                    buyTpLevel = double.NaN;
                    buySlLevel = double.NaN;
                }
            }

            // Check sell position TP/SL
            if (activeSellPosition)
            {
                if (!double.IsNaN(sellTpLevel) && Low[0] <= sellTpLevel && High[0] >= sellTpLevel)
                {
                    CreateSignalDot("TP", sellTpLevel, false);
                    activeSellPosition = false;
                    sellTpLevel = double.NaN;
                    sellSlLevel = double.NaN;
                }
                else if (!double.IsNaN(sellSlLevel) && High[0] >= sellSlLevel && Low[0] <= sellSlLevel)
                {
                    CreateSignalDot("SL", sellSlLevel, false);
                    activeSellPosition = false;
                    sellTpLevel = double.NaN;
                    sellSlLevel = double.NaN;
                }
            }
        }

        private void CreateSignalDot(string signalType, double priceLevel, bool isDelayed)
        {
            Brush signalColor = Brushes.White;
            string arrowText = "▶";

            switch (signalType)
            {
                case "BUY":
                    signalColor = Brushes.Green;
                    break;
                case "SELL":
                    signalColor = Brushes.Red;
                    break;
                case "TP":
                    signalColor = Brushes.Blue;
                    arrowText = "TP";
                    break;
                case "SL":
                    signalColor = Brushes.Orange;
                    arrowText = "SL";
                    break;
            }

            // Position arrow one bar to the left
            int arrowPosition = Math.Max(0, CurrentBar - 1);

            string signalTag = $"Signal_{signalType}_{CurrentBar}_{Time[0].Ticks}";
            Draw.Text(this, signalTag, false, arrowText, arrowPosition, priceLevel, 0, signalColor,
                     new SimpleFont("Arial", 12), TextAlignment.Center, Brushes.Transparent, Brushes.Transparent, 0);
            objectTagQueue.Enqueue(signalTag);

            // Log signal
            string signalDesc = signalType + (isDelayed ? " (Delayed)" : "");
            Print($"{signalDesc} signal at {priceLevel:F3} on bar {CurrentBar}");
        }

        private void CleanupOldObjects()
        {
            // Remove old objects to maintain performance
            while (objectTagQueue.Count > maxBarsBack * 10) // Estimate max objects per bar
            {
                string oldTag = objectTagQueue.Dequeue();
                if (!string.IsNullOrEmpty(oldTag))
                {
                    RemoveDrawObject(oldTag);
                }
            }
        }

        #region Properties
        [NinjaScriptProperty]
        [Display(Name = "Show Fibonacci Levels", Description = "Show Fibonacci Levels", Order = 1, GroupName = "Display Settings")]
        public bool ShowLevels
        {
            get { return showLevels; }
            set { showLevels = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Show Level Labels", Description = "Show Level Labels", Order = 2, GroupName = "Display Settings")]
        public bool ShowLabels
        {
            get { return showLabels; }
            set { showLabels = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Show Trend Lines", Description = "Show Trend Lines", Order = 3, GroupName = "Display Settings")]
        public bool ShowTrendlines
        {
            get { return showTrendlines; }
            set { showTrendlines = value; }
        }

        [NinjaScriptProperty]
        [Range(1, 100)]
        [Display(Name = "Maximum Bars to Display", Description = "Maximum Bars to Display", Order = 4, GroupName = "Display Settings")]
        public int MaxBarsBack
        {
            get { return maxBarsBack; }
            set { maxBarsBack = value; }
        }

        [NinjaScriptProperty]
        [Range(1, 3)]
        [Display(Name = "Line Width", Description = "Line Width", Order = 5, GroupName = "Display Settings")]
        public int LineWidth
        {
            get { return lineWidth; }
            set { lineWidth = value; }
        }

        [NinjaScriptProperty]
        [Range(1, 5)]
        [Display(Name = "Trend Line Width", Description = "Trend Line Width", Order = 6, GroupName = "Display Settings")]
        public int TrendlineWidth
        {
            get { return trendlineWidth; }
            set { trendlineWidth = value; }
        }

        [NinjaScriptProperty]
        [Range(0, 100)]
        [Display(Name = "Line Transparency", Description = "Line Transparency", Order = 7, GroupName = "Display Settings")]
        public int Transparency
        {
            get { return transparency; }
            set { transparency = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Use Multi-Timeframe Mode", Description = "Enable to use MTF candle logic for Fibonacci drawing", Order = 1, GroupName = "Mode Settings")]
        public bool UseMtfMode
        {
            get { return useMtfMode; }
            set { useMtfMode = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "MTF Period Type", Description = "MTF Period Type", Order = 2, GroupName = "Mode Settings")]
        public BarsPeriodType MtfPeriodType
        {
            get { return mtfPeriodType; }
            set { mtfPeriodType = value; }
        }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "MTF Period Value", Description = "MTF Period Value", Order = 3, GroupName = "Mode Settings")]
        public int MtfPeriodValue
        {
            get { return mtfPeriodValue; }
            set { mtfPeriodValue = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Use Confirmed MTF Data", Description = "True = No repainting but 1 candle delay. False = Real-time but repaints", Order = 4, GroupName = "Mode Settings")]
        public bool UseConfirmedData
        {
            get { return useConfirmedData; }
            set { useConfirmedData = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Use Higher Timeframe Range (Legacy)", Description = "Legacy mode: Enable to draw Fibonacci levels based on higher timeframe high/low", Order = 1, GroupName = "Legacy Settings")]
        public bool UseHigherTimeframe
        {
            get { return useHigherTimeframe; }
            set { useHigherTimeframe = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "HTF Period Type", Description = "HTF Period Type", Order = 2, GroupName = "Legacy Settings")]
        public BarsPeriodType HtfPeriodType
        {
            get { return htfPeriodType; }
            set { htfPeriodType = value; }
        }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "HTF Period Value", Description = "HTF Period Value", Order = 3, GroupName = "Legacy Settings")]
        public int HtfPeriodValue
        {
            get { return htfPeriodValue; }
            set { htfPeriodValue = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Show Buy/Sell Signals", Description = "Show Buy/Sell Signals", Order = 1, GroupName = "Signal Settings")]
        public bool ShowSignals
        {
            get { return showSignals; }
            set { showSignals = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Show Immediate Signals", Description = "Generate signals when current bar hits green/red lines", Order = 2, GroupName = "Signal Settings")]
        public bool ShowImmediateSignals
        {
            get { return showImmediateSignals; }
            set { showImmediateSignals = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Show Delayed Signals", Description = "Generate signals when next candle after HTF close hits previous green/red lines", Order = 3, GroupName = "Signal Settings")]
        public bool ShowDelayedSignals
        {
            get { return showDelayedSignals; }
            set { showDelayedSignals = value; }
        }
        #endregion
    }
}
